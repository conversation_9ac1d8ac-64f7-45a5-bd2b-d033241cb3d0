<?php
/**
 * 清理缓存脚本（无Redis版本）
 */

echo "清理FastAdmin缓存\n";
echo "================\n\n";

// 1. 清理模板缓存
echo "1. 清理模板缓存...\n";
$tempDir = __DIR__ . '/runtime/temp';
if (is_dir($tempDir)) {
    $files = glob($tempDir . '/*');
    $count = 0;
    foreach ($files as $file) {
        if (is_file($file)) {
            unlink($file);
            $count++;
        }
    }
    echo "   清理了 {$count} 个模板缓存文件\n";
} else {
    echo "   模板缓存目录不存在\n";
}

// 2. 清理文件缓存
echo "2. 清理文件缓存...\n";
$cacheDir = __DIR__ . '/runtime/cache';
if (is_dir($cacheDir)) {
    $files = glob($cacheDir . '/*');
    $count = 0;
    foreach ($files as $file) {
        if (is_file($file)) {
            unlink($file);
            $count++;
        }
    }
    echo "   清理了 {$count} 个文件缓存\n";
} else {
    echo "   文件缓存目录不存在\n";
}

// 3. 清理日志文件（可选）
echo "3. 清理大日志文件...\n";
$logDir = __DIR__ . '/runtime/log';
if (is_dir($logDir)) {
    $files = glob($logDir . '/*.log');
    $count = 0;
    foreach ($files as $file) {
        if (filesize($file) > 5 * 1024 * 1024) { // 大于5MB
            unlink($file);
            $count++;
        }
    }
    echo "   清理了 {$count} 个大日志文件\n";
} else {
    echo "   日志目录不存在\n";
}

echo "\n✅ 缓存清理完成！\n";
echo "现在可以尝试访问网站了。\n";
