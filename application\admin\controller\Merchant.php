<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 
 *
 * @icon fa fa-circle-o
 */
class Merchant extends Backend
{

    /**
     * Merchant模型对象
     * @var \app\admin\model\Merchant
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Merchant;

    }

    public function index()
{
    $this->request->filter(['strip_tags', 'trim']);
    if (!$this->request->isAjax()) {
        return $this->view->fetch();
    }
    if ($this->request->request('keyField')) {
        return $this->selectpage();
    }

    [$where, $sort, $order, $offset, $limit] = $this->buildparams();

    // 判断是否超级管理员
    $isSuperAdmin = $this->auth->isSuperAdmin();

    // 如果不是超级管理员，则添加 admin_id 限制
    if (is_callable($where)) {
        $oldWhere = $where;
        $where = function($query) use ($oldWhere, $isSuperAdmin) {
            $oldWhere($query);
            if (!$isSuperAdmin) {
                $query->where('admin_id', '=', $this->auth->id);
            }
        };
    } else {
        if (!$isSuperAdmin) {
            $where[] = ['admin_id', '=', $this->auth->id];
        }
    }

    // 获取数据
    $list = $this->model
        ->where($where)
        ->order($sort, $order)
        ->paginate($limit);

    $result = ['total' => $list->total(), 'rows' => $list->items()];

    return json($result);
}



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}
 