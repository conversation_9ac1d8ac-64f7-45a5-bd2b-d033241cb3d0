<?php

namespace app\admin\model;

use think\Model;


class Merchant extends Model
{

    

    

    // 表名
    protected $name = 'merchant';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'nickname_text'
    ];

    // 关联管理员
    public function admin()
    {
        return $this->belongsTo('Admin', 'admin_id', 'id', '', 'LEFT')->setEagerlyType(0);
    }

    // 跟进者昵称
    public function getNicknameTextAttr($value, $data)
    {
        if (isset($data['admin_id']) && $data['admin_id']) {
            $admin = $this->admin()->where('id', $data['admin_id'])->find();
            return $admin ? $admin['nickname'] : '';
        }
        return '';
    }

    // 自动写入admin_id
    protected static function init()
    {
        parent::init();
        self::beforeInsert(function ($row) {
            $now = date('Y-m-d H:i:s');
            if (empty($row['create_time'])) {
                $row['create_time'] = $now;
            }
            if (empty($row['update_time'])) {
                $row['update_time'] = $now;
            }
            // 自动写入当前登录管理员id
            if (empty($row['admin_id'])) {
                $adminId = session('admin.id');
                if ($adminId) {
                    $row['admin_id'] = $adminId;
                }
            }
        });
        self::beforeUpdate(function ($row) {
            $row['update_time'] = date('Y-m-d H:i:s');
        });
    }


}
 