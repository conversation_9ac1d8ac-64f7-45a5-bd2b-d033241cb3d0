<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{$currentCategory.name|htmlentities} - {$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {margin:0;padding:0;box-sizing:border-box;}
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        /* 导航栏 */
        .navbar {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 0 40px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .navbar .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            text-decoration: none;
        }
        
        .navbar .nav {
            display: flex;
            gap: 0;
            margin: 0;
            padding: 0;
        }
        
        .navbar .nav>li {
            position: relative;
            list-style: none;
        }
        
        .navbar .nav>li>a {
            display: block;
            color: #333;
            font-weight: 500;
            font-size: 16px;
            padding: 25px 20px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .navbar .nav>li>a:hover,
        .navbar .nav>li>a.active {
            background: #3498db;
            color: white;
        }
        
        /* 下拉菜单 */
        .navbar .nav>li .dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            min-width: 180px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1001;
        }
        
        .navbar .nav>li:hover .dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .navbar .nav>li .dropdown li {
            list-style: none;
        }
        
        .navbar .nav>li .dropdown li a {
            display: block;
            padding: 12px 20px;
            color: #333;
            font-size: 14px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .navbar .nav>li .dropdown li a:hover,
        .navbar .nav>li .dropdown li a.active {
            background: #f8f9fa;
            color: #3498db;
        }
        
        /* 主容器 */
        .main-container {
            max-width: 1200px;
            margin: 30px auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
        }
        
        /* 左侧边栏 */
        .sidebar {
            width: 250px;
            background: #f8f9fa;
            border-right: 1px solid #e0e6ed;
            flex-shrink: 0;
        }
        
        .breadcrumb {
            padding: 20px;
            background: #fff;
            border-bottom: 1px solid #e0e6ed;
            font-size: 14px;
            color: #666;
        }
        
        .breadcrumb a {
            color: #3498db;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .breadcrumb-sep {
            margin: 0 8px;
            color: #ccc;
        }
        
        .category-header {
            padding: 20px;
            border-bottom: 1px solid #e0e6ed;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .category-desc {
            font-size: 13px;
            color: #666;
        }
        
        /* 分类导航 */
        .category-nav {
            padding: 20px;
        }
        
        .category-nav h4 {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }
        
        .category-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .category-list li {
            margin-bottom: 5px;
        }
        
        .category-list a {
            display: block;
            padding: 10px 15px;
            color: #666;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .category-list a:hover,
        .category-list a.active {
            background: #3498db;
            color: white;
        }
        
        /* 右侧内容区域 */
        .content-area {
            flex: 1;
            padding: 30px;
        }
        
        /* 筛选区域 */
        .filter-section {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .filter-label {
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            font-size: 14px;
            color: #333;
        }
        
        .products-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }
        
        .products-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .products-count {
            color: #666;
            font-size: 14px;
        }

        /* 筛选栏样式 */
        .filter-bar {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .filter-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-item label {
            font-weight: 500;
            color: #495057;
            font-size: 14px;
        }

        .filter-item select {
            padding: 6px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background: white;
            font-size: 14px;
            color: #495057;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        .filter-item select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        
        /* 产品列表 - 横向排列 */
        .product-list {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .product-card {
            width: 260px;
            background: white;
            border: 1px solid #e0e6ed;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #3498db;
        }
        
        .image-container {
            width: 100%;
            height: 160px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .content {
            padding: 15px;
        }
        
        .title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
            height: 44px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        
        .price {
            font-size: 20px;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 15px;
        }
        
        .commission-row {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 8px 0;
            border-top: 1px solid #f1f5f9;
            border-bottom: 1px solid #f1f5f9;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: #666;
        }

        .stat-item i {
            color: #3498db;
        }
        
        .commission-block {
            flex: 1;
            background: #f8f9fa;
            border-radius: 6px;
            padding: 8px;
            text-align: center;
        }
        
        .commission-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }
        
        .commission-value {
            font-size: 14px;
            font-weight: bold;
            color: #27ae60;
        }
        
        .meta-info {
            font-size: 13px;
            color: #666;
            margin-bottom: 10px;
            line-height: 1.4;
        }
        
        .meta-label {
            font-weight: 500;
        }
        
        .bottom-info {
            font-size: 12px;
            color: #999;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .script-btn {
            width: 100%;
            background: #3498db;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .script-btn:hover {
            background: #2980b9;
        }

        .product-link-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-decoration: none;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-block;
            margin-top: 5px;
        }

        .product-link-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }

        .product-link-disabled {
            color: #999;
            font-size: 11px;
            font-style: italic;
        }
        
        /* 空状态 */
        .no-products {
            text-align: center;
            padding: 80px 20px;
            color: #666;
        }
        
        .no-products i {
            font-size: 64px;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        /* 分页 */
        .pagination-wrapper {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        /* 页脚 */
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 30px 0;
            margin-top: 50px;
        }
    </style>
</head>
<body>
<div class="navbar">
    <a href="/index.php" class="logo">{$site.name|htmlentities}</a>
    <ul class="nav">
        <li><a href="/index.php">首页</a></li>
        {volist name="categories" id="cat"}
        <li>
            {if !empty($cat.children)}
                <a href="javascript:void(0)" {if condition="$currentCategory.id eq $cat.id"}class="active"{/if}>{$cat.name|htmlentities}</a>
                <ul class="dropdown">
                    {volist name="cat.children" id="child"}
                    <li>
                        <a href="/index.php/index/index/category?id={$child.id}" {if condition="$currentCategory.id eq $child.id"}class="active"{/if}>{$child.name|htmlentities}</a>
                    </li>
                    {/volist}
                </ul>
            {else/}
                <a href="/index.php/index/index/category?id={$cat.id}" {if condition="$currentCategory.id eq $cat.id"}class="active"{/if}>{$cat.name|htmlentities}</a>
            {/if}
        </li>
        {/volist}
    </ul>
</div>

<div class="main-container">
    <!-- 左侧边栏 -->
    <div class="sidebar">
        <div class="breadcrumb">
            <a href="/index.php"><i class="fas fa-home"></i> 首页</a>
            <span class="breadcrumb-sep">></span>
            <span>{$currentCategory.name|htmlentities}</span>
        </div>
        
        <div class="category-header">
            <h1 class="page-title">{$currentCategory.name|htmlentities}</h1>
            <div class="category-desc">浏览该分类下的所有产品</div>
        </div>
        
        <div class="category-nav">
            <h4>相关分类</h4>
            <ul class="category-list">
                {volist name="categories" id="cat"}
                {if !empty($cat.children)}
                    {volist name="cat.children" id="child"}
                    <li>
                        <a href="/index.php/index/index/category?id={$child.id}" {if condition="$currentCategory.id eq $child.id"}class="active"{/if}>
                            {$child.name|htmlentities}
                        </a>
                    </li>
                    {/volist}
                {else/}
                    <li>
                        <a href="/index.php/index/index/category?id={$cat.id}" {if condition="$currentCategory.id eq $cat.id"}class="active"{/if}>
                            {$cat.name|htmlentities}
                        </a>
                    </li>
                {/if}
                {/volist}
            </ul>
        </div>
    </div>
    
    <!-- 右侧内容区域 -->
    <div class="content-area">
        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-group">
                <span class="filter-label">排序:</span>
                <select class="filter-select">
                    <option>综合排序</option>
                    <option>价格从低到高</option>
                    <option>价格从高到低</option>
                    <option>最新上架</option>
                </select>
            </div>
            
            <div class="filter-group">
                <span class="filter-label">佣金:</span>
                <select class="filter-select">
                    <option>全部佣金</option>
                    <option>5%以上</option>
                    <option>10%以上</option>
                    <option>15%以上</option>
                </select>
            </div>
        </div>
        
        <!-- 产品列表 -->
        {if isset($msg)}
        <div class="no-products">
            <i class="fas fa-exclamation-triangle"></i>
            <div style="font-size:20px;font-weight:600;margin-bottom:10px;">{$msg}</div>
            <div>请浏览其他分类或稍后再来~</div>
        </div>
        {elseif condition="empty($products)"}
        <div class="no-products">
            <i class="fas fa-box-open"></i>
            <div style="font-size:20px;font-weight:600;margin-bottom:10px;">该分类下暂无产品</div>
            <div>请浏览其他分类或稍后再来~</div>
        </div>
        {else/}
        <div class="products-header">
            <h2 class="products-title">{$currentCategory.name|htmlentities} 产品</h2>
            <span class="products-count">共 {$products|count} 个产品</span>
        </div>

        <!-- 排序和筛选 -->
        <div class="filter-bar">
            <div class="filter-item">
                <label>排序:</label>
                <select id="sortSelect" onchange="applyFilter()">
                    <option value="id" {if $currentSort == 'id'}selected{/if}>默认排序</option>
                    <option value="price_asc" {if $currentSort == 'price_asc'}selected{/if}>价格从低到高</option>
                    <option value="price_desc" {if $currentSort == 'price_desc'}selected{/if}>价格从高到低</option>
                    <option value="commission_desc" {if $currentSort == 'commission_desc'}selected{/if}>佣金从高到低</option>
                    <option value="sales_desc" {if $currentSort == 'sales_desc'}selected{/if}>销量从高到低</option>
                </select>
            </div>
            <div class="filter-item">
                <label>佣金:</label>
                <select id="commissionSelect" onchange="applyFilter()">
                    <option value="" {if $currentCommission == ''}selected{/if}>全部佣金</option>
                    <option value="10" {if $currentCommission == '10'}selected{/if}>10%以上</option>
                    <option value="15" {if $currentCommission == '15'}selected{/if}>15%以上</option>
                    <option value="20" {if $currentCommission == '20'}selected{/if}>20%以上</option>
                </select>
            </div>
        </div>

        <div class="product-list">
            {volist name="products" id="product"}
            <div class="product-card" data-script="{$product.video_text|default=''}">
                <div class="image-container">
                    <img src="{$product.image|htmlentities}" alt="{$product.name|htmlentities}" onerror="this.style.display='none'; this.parentNode.innerHTML='<div style=\'font-size:48px;color:#ccc;\'><i class=\'fas fa-image\'></i></div>';">
                </div>
                <div class="content">
                    <div class="title">{$product.name|htmlentities}</div>
                    <div class="price">Rp{$product.price}</div>

                    <!-- 样品数和销量 -->
                    <div class="stats-row">
                        <div class="stat-item">
                            <i class="fas fa-box"></i>
                            <span>可用样品: {$product.sample_num|default=0}</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-chart-line"></i>
                            <span>销量: {$product.sale_count|default=0}</span>
                        </div>
                    </div>

                    <div class="commission-row">
                        <div class="commission-block">
                            <div class="commission-label">公开佣金</div>
                            <div class="commission-value">{$product.public_commission}%</div>
                        </div>
                        <div class="commission-block">
                            <div class="commission-label">TAP佣金</div>
                            <div class="commission-value">{$product.commission}%</div>
                        </div>
                    </div>
                    <div class="meta-info">
                        <span class="meta-label">商家名称：</span>{$product.merchant.name|default='未知'}<br>
                        <span class="meta-label">跟进者：</span>{$product.admin.nickname|default='-'}
                    </div>
                    <div class="bottom-info">
                        <span>活动ID：{$product.active_id|default='-'}</span>
                        {if $product.product_id}
                        <a href="https://shop-id.tokopedia.com/view/product/{$product.product_id}" target="_blank" class="product-link-btn">查看产品链接</a>
                        {else/}
                        <span class="product-link-disabled">产品链接不可用</span>
                        {/if}
                    </div>
                    <button class="script-btn" onclick="showScript(this)">查看脚本</button>
                </div>
            </div>
            {/volist}
        </div>
        
        <!-- 分页 -->
        {if isset($pagination)}
        <div class="pagination-wrapper">
            {$pagination}
        </div>
        {/if}
        {/if}
    </div>
</div>

<!-- 页脚 -->
<footer class="footer">
    <p>&copy; 2024 {$site.name|htmlentities}. 保留所有权利.</p>
    <p style="margin-top:5px;opacity:0.8;">让购物成为一种享受</p>
</footer>

<script>
// 查看脚本功能
function showScript(btn) {
    const card = btn.closest('.product-card');
    const script = card.getAttribute('data-script');
    
    if (script && script.trim()) {
        // 创建模态框显示脚本
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;
        
        modal.innerHTML = `
            <div style="
                background: white;
                border-radius: 8px;
                padding: 30px;
                max-width: 600px;
                max-height: 80vh;
                overflow-y: auto;
                position: relative;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            ">
                <button onclick="this.closest('.modal').remove()" style="
                    position: absolute;
                    top: 15px;
                    right: 15px;
                    background: none;
                    border: none;
                    font-size: 20px;
                    cursor: pointer;
                    color: #999;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                ">×</button>
                <h3 style="margin-bottom: 20px; color: #333;">产品脚本</h3>
                <div style="
                    background: #f8f9fa;
                    border-radius: 6px;
                    padding: 20px;
                    font-family: monospace;
                    font-size: 14px;
                    line-height: 1.6;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                    color: #333;
                    border: 1px solid #e9ecef;
                ">${script}</div>
            </div>
        `;
        
        modal.className = 'modal';
        document.body.appendChild(modal);
        
        // 点击背景关闭
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.remove();
            }
        });
    } else {
        alert('该产品暂无脚本信息');
    }
}

// 应用筛选
function applyFilter() {
    const sort = document.getElementById('sortSelect').value;
    const commission = document.getElementById('commissionSelect').value;

    // 构建URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const categoryId = urlParams.get('id');

    let url = '/index.php/index/index/category?id=' + categoryId;
    if (sort && sort !== 'id') {
        url += '&sort=' + sort;
    }
    if (commission) {
        url += '&commission=' + commission;
    }
    // 重置到第一页（筛选后从第一页开始）
    url += '&page=1';

    // 跳转到新URL
    window.location.href = url;
}
</script>
</body>
</html> 


