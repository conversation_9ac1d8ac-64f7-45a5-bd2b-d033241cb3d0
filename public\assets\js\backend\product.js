define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'product/index' + location.search,
                    add_url: 'product/add',
                    edit_url: 'product/edit',
                    del_url: 'product/del',
                    multi_url: 'product/multi',
                    import_url: 'product/import',
                    table: 'product',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'image', title: __('Image'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'price', title: __('Price'), operate:'BETWEEN'},
                        {field: 'public_commission', title: __('Public_commission'), operate:'BETWEEN'},
                        {field: 'commission', title: 'TAP佣金', operate:'BETWEEN'},
                        {field: 'sample_total', title: __('Sample_total')},
                        {field: 'sample_num', title: __('Sample_num')},
                        {field: 'sale_count', title: __('Sale_count')},
                        {field: 'status', title: __('Status'), formatter: function(value) { return value == 1 ? '已上架' : '未上架'; }},
                        {field: 'product_id', title: __('Product_id'), operate: 'LIKE'},
                        {field: 'active_id', title: __('Active_id'), operate: 'LIKE'},
                        {field: 'catelog.name', title: '所属分类', operate: 'LIKE'},
                        {field: 'merchant.name', title: '所属商家', operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'create_time', title: __('Create_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: function(value){
                            if (!value) return '';
                            if (/^\d{4}-\d{2}-\d{2}/.test(value)) return value;
                            if (/^\d+$/.test(value)) {
                                var d = new Date(value * 1000);
                                return d.getFullYear() + '-' + ('0'+(d.getMonth()+1)).slice(-2) + '-' + ('0'+d.getDate()).slice(-2) + ' ' + ('0'+d.getHours()).slice(-2) + ':' + ('0'+d.getMinutes()).slice(-2) + ':' + ('0'+d.getSeconds()).slice(-2);
                            }
                            return value;
                        }},
                        {field: 'update_time', title: __('Update_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: function(value){
                            if (!value) return '';
                            if (/^\d{4}-\d{2}-\d{2}/.test(value)) return value;
                            if (/^\d+$/.test(value)) {
                                var d = new Date(value * 1000);
                                return d.getFullYear() + '-' + ('0'+(d.getMonth()+1)).slice(-2) + '-' + ('0'+d.getDate()).slice(-2) + ' ' + ('0'+d.getHours()).slice(-2) + ':' + ('0'+d.getMinutes()).slice(-2) + ':' + ('0'+d.getSeconds()).slice(-2);
                            }
                            return value;
                        }},
                        {field: 'admin.nickname', title: '所属人员', operate: 'LIKE'},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate},
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
