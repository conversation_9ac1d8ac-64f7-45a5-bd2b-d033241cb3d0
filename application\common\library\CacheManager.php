<?php

namespace app\common\library;

use think\Cache;
use think\Config;

/**
 * 缓存管理类
 */
class CacheManager
{
    /**
     * 缓存配置数据
     * @param string $key 配置键名
     * @param mixed $data 数据
     * @param int $expire 过期时间
     * @return bool
     */
    public static function setConfig($key, $data, $expire = 3600)
    {
        return Cache::set('config:' . $key, $data, $expire);
    }
    
    /**
     * 获取缓存的配置数据
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function getConfig($key, $default = null)
    {
        return Cache::get('config:' . $key, $default);
    }
    
    /**
     * 缓存菜单数据
     * @param int $adminId 管理员ID
     * @param array $menus 菜单数据
     * @param int $expire 过期时间
     * @return bool
     */
    public static function setMenus($adminId, $menus, $expire = 1800)
    {
        return Cache::set('menus:' . $adminId, $menus, $expire);
    }
    
    /**
     * 获取缓存的菜单数据
     * @param int $adminId 管理员ID
     * @return array|null
     */
    public static function getMenus($adminId)
    {
        return Cache::get('menus:' . $adminId);
    }
    
    /**
     * 缓存权限数据
     * @param int $adminId 管理员ID
     * @param array $rules 权限规则
     * @param int $expire 过期时间
     * @return bool
     */
    public static function setRules($adminId, $rules, $expire = 1800)
    {
        return Cache::set('rules:' . $adminId, $rules, $expire);
    }
    
    /**
     * 获取缓存的权限数据
     * @param int $adminId 管理员ID
     * @return array|null
     */
    public static function getRules($adminId)
    {
        return Cache::get('rules:' . $adminId);
    }
    
    /**
     * 清除用户相关缓存
     * @param int $adminId 管理员ID
     * @return bool
     */
    public static function clearUserCache($adminId)
    {
        Cache::rm('menus:' . $adminId);
        Cache::rm('rules:' . $adminId);
        return true;
    }
    
    /**
     * 清除所有配置缓存
     * @return bool
     */
    public static function clearConfigCache()
    {
        return Cache::clear();
    }
    
    /**
     * 获取缓存统计信息
     * @return array
     */
    public static function getStats()
    {
        $cacheType = Config::get('cache.type');
        return [
            'type' => $cacheType,
            'status' => $cacheType !== 'File' ? 'enabled' : 'file_cache',
        ];
    }
}
