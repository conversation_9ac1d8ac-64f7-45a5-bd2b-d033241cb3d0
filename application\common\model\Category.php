<?php

namespace app\common\model;

use think\Model;

/**
 * 分类模型
 */
class Category extends Model
{

    // 表名
    protected $name = 'category';
    
    // 启用自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
   
    
    // 显式设置可见字段，确保status可用
    protected $visible = ['id', 'name', 'status', 'pid', 'weigh', 'createtime', 'updatetime'];


    
    protected static function init()
    {
        self::afterInsert(function ($row) {
            // 兼容没有 weigh 字段的情况
            if (!isset($row['weigh']) || !$row['weigh']) {
                $row->save(['weigh' => $row['id']]);
            }
        });
    }
}
