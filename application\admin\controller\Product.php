<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 
 *
 * @icon fa fa-circle-o
 */
class Product extends Backend
{

    /**
     * Product模型对象
     * @var \app\admin\model\Product
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Product;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
{
    // 当前是否为关联查询
    $this->relationSearch = true;

    // 设置过滤器
    $this->request->filter(['strip_tags', 'trim']);

    if ($this->request->isAjax()) {

        // 如果是 Selectpage 请求，转发处理
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }

        // 构建查询参数
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();

        // 判断是否为超级管理员
        $isSuperAdmin = $this->auth->isSuperAdmin();

        // 非超级管理员加上 admin_id 限制（兼容闭包或数组形式）
        if (is_callable($where)) {
            $oldWhere = $where;
            $where = function ($query) use ($oldWhere, $isSuperAdmin) {
                $oldWhere($query);
                if (!$isSuperAdmin) {
                    $query->where('product.admin_id', '=', $this->auth->id);
                }
            };
        } else {
            if (!$isSuperAdmin) {
                $where[] = ['product.admin_id', '=', $this->auth->id];
            }
        }

        // 查询数据（带关联模型）
        $list = $this->model
            ->with(['catelog', 'admin', 'merchant'])
            ->where($where)
            ->order($sort, $order)
            ->paginate($limit);

        // 处理关联展示字段
        foreach ($list as $row) {
            $row->getRelation('admin')->visible(['nickname']);
            $row->getRelation('merchant')->visible(['name']);
        }

        return json([
            'total' => $list->total(),
            'rows'  => $list->items(),
        ]);
    }

    // 渲染视图
    return $this->view->fetch();
}


}
