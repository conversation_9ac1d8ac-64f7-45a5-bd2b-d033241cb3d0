<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\model\Category as CategoryModel;
use fast\Tree;

/**
 * 分类管理
 *
 * @icon   fa fa-list
 * @remark 用于管理网站的所有分类,分类可进行无限级分类,分类类型请在常规管理->系统配置->字典配置中添加
 */
class Category extends Backend
{

    /**
     * @var \app\common\model\Category
     */
    protected $model = null;
    protected $categorylist = [];
    protected $noNeedRight = ['selectpage'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = model('app\common\model\Category');

        $tree = Tree::instance();
        $tree->init(collection($this->model->order('weigh desc,id desc')->select())->toArray(), 'pid');
        $this->categorylist = $tree->getTreeList($tree->getTreeArray(0), 'name');
        $categorydata = [0 => ['name' => __('None')]];
        foreach ($this->categorylist as $k => $v) {
            // 去除 name 字段中的 &nbsp; 及空格
            $v['name'] = str_replace(['&nbsp;', ' ', ' '], '', $v['name']);
            $categorydata[$v['id']] = $v;
        }
        $this->view->assign("parentList", $categorydata);
    }

    /**
     * 查看
     */
    public function index()
    {
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            // 用Tree工具类处理为带缩进的树形结构
            $list = $this->model->order('weigh desc,id desc')->select();
            $list = collection($list)->toArray();
            $tree = new \fast\Tree();
            $tree->init($list, 'pid');
            $list = $tree->getTreeList($tree->getTreeArray(0), 'name');
            $result = ["total" => count($list), "rows" => $list];
            return json($result);
        }
        // 非Ajax请求，分配list变量给模板
        $list = $this->model->order('id desc')->select();
        $this->view->assign('list', $list);
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            return parent::add();
        }
        $pid = $this->request->get('pid', 0);
        $this->view->assign('pid', $pid);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $this->token();
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                if (!isset($params['pid'])) {
                    $params['pid'] = 0;
                }

                if ($params['pid'] != $row['pid']) {
                    $childrenIds = Tree::instance()->init(collection(\app\common\model\Category::select())->toArray())->getChildrenIds($row['id'], true);
                    if (in_array($params['pid'], $childrenIds)) {
                        $this->error(__('Can not change the parent to child or itself'));
                    }
                }

                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    if ($result !== false) {
                        // 修改：编辑成功后跳转到分类列表页
                        $this->success('编辑成功', url('category/index'));
                    } else {
                        $this->error($row->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 切换状态
     */
    public function switchStatus()
    {
        $id = $this->request->post('id');
        $status = $this->request->post('status');
        $category = $this->model->find($id);
        if (!$category) {
            return json(['code'=>0,'msg'=>'分类不存在']);
        }
        $category->status = $status;
        $category->save();
        return json(['code'=>1,'msg'=>'操作成功']);
    }

    /**
     * Selectpage搜索
     *
     * @internal
     */
    public function selectpage()
    {
        // 获取参数
        $isleaf = $this->request->param('isleaf', 0);
        $list = $this->model->field('id,pid,name')->select();
        if ($isleaf) {
            // 计算所有有子类的id
            $hasChildIds = array_column($list, 'pid');
            $list = array_filter($list, function($item) use ($hasChildIds) {
                return !in_array($item['id'], $hasChildIds);
            });
        }
        // 组装selectpage格式
        $result = [];
        foreach ($list as $item) {
            $result[] = [
                'id' => $item['id'],
                'name' => $item['name'],
                'pid' => $item['pid'],
            ];
        }
        return json(['list' => $result, 'total' => count($result)]);
    }
}
