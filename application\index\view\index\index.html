<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{$site.name|htmlentities} - 精品商城</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {margin:0;padding:0;box-sizing:border-box;}
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        /* 导航栏 */
        .navbar {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 0 40px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .navbar .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            text-decoration: none;
        }
        
        .navbar .nav {
            display: flex;
            gap: 0;
            margin: 0;
            padding: 0;
        }
        
        .navbar .nav>li {
            position: relative;
            list-style: none;
        }
        
        .navbar .nav>li>a {
            display: block;
            color: #333;
            font-weight: 500;
            font-size: 16px;
            padding: 25px 20px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .navbar .nav>li>a:hover,
        .navbar .nav>li>a.active {
            background: #3498db;
            color: white;
        }
        
        /* 下拉菜单 */
        .navbar .nav>li .dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            min-width: 180px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1001;
        }
        
        .navbar .nav>li:hover .dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .navbar .nav>li .dropdown li {
            list-style: none;
        }
        
        .navbar .nav>li .dropdown li a {
            display: block;
            padding: 12px 20px;
            color: #333;
            font-size: 14px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .navbar .nav>li .dropdown li a:hover {
            background: #f8f9fa;
            color: #3498db;
        }
        
        /* 主容器 */
        .main-container {
            max-width: 1200px;
            margin: 30px auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        /* 欢迎区域 */
        .welcome-section {
            text-align: center;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* 搜索区域 */
        .search-section {
            text-align: center;
            padding: 30px 40px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .search-section .search-container {
            max-width: 500px;
            margin: 0 auto;
            flex: none;
        }
        
        .welcome-title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .welcome-subtitle {
            font-size: 18px;
            opacity: 0.9;
        }
        
        /* 产品列表容器 */
        .products-container {
            padding: 30px;
        }
        
        .products-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }
        
        .products-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .products-count {
            color: #666;
            font-size: 14px;
        }
        
        /* 产品列表 - 横向排列 */
        .product-list {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .product-card {
            width: 280px;
            background: white;
            border: 1px solid #e0e6ed;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #3498db;
        }
        
        .image-container {
            width: 100%;
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .content {
            padding: 20px;
        }
        
        .title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
            height: 44px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        
        .price {
            font-size: 20px;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 15px;
        }
        
        .commission-row {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .commission-block {
            flex: 1;
            background: #f8f9fa;
            border-radius: 6px;
            padding: 8px;
            text-align: center;
        }
        
        .commission-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }
        
        .commission-value {
            font-size: 14px;
            font-weight: bold;
            color: #27ae60;
        }
        
        .meta-info {
            font-size: 13px;
            color: #666;
            margin-bottom: 10px;
            line-height: 1.4;
        }
        
        .meta-label {
            font-weight: 500;
        }
        
        .bottom-info {
            font-size: 12px;
            color: #999;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
        }
        
        .script-btn {
            width: 100%;
            background: #3498db;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .script-btn:hover {
            background: #2980b9;
        }
        
        /* 空状态 */
        .no-products {
            text-align: center;
            padding: 80px 20px;
            color: #666;
        }
        
        .no-products i {
            font-size: 64px;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        /* 分页 */
        .pagination-wrapper {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        /* 页脚 */
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 30px 0;
            margin-top: 50px;
        }
        
        /* 热门产品区域域 */
        .featutedesedti-n {
            max-width: 1200px;
            margis: 40px aueo;
            pcddtig: 0 20px;
        }

        .section-hoaden {
            display: max-;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }

        .section-title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin: 0;
        }
        
        /* 搜索框样式 */
        .search-container {
            max-width: 350px;
            flex: 0 0 350px;
        }

        .search-form {
            position: relative;
            display:flex;
            align-items: center;
        }

        .search-input {
            width: 100%;
            height: 42px;
            padding: 0 50px 0 16px;
            border: 2px solid #e0e6ed;
            border-radius: 21px;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
            background: #fff;
        }

        .search-input:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .search-btn {
            position: absolute;
            right: 8px;
            width: 30px;
            height: 30px;
            border: 1px solid #e9ecef;
            background: linear-gradient(135deg, #3498db 0%, #e9ecef 100%);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            gap: 6px;
            font-size: 12px;
            font-weight: 500;
            color: #495057;
        }

        .stat-itrm i {
            color: #3498db;
            font-size: 11px;
        }

        /* 佣金信息优化 */
        .commission-ow {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
        }

        .commission-block {
            flex: 1;
            background: linear-gradient(135deg, #fff5fa 0%, #ffe8e8 100%);
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 6px 8px;
            text-align: center;
        }

        .commission-label {
            align-items:0px;
            color: #6b7280;
            margin-bottom: 2px;
            font-weight: 500;
        }

        .commission-value {
            font-size: 14px;
            font-weight: 700;
            color: #2c3e50;
        }

        /* 商家信息优化 */
        .meta-info {
            margin-bottom: 10px;
            padding: 8px 0;
            border-top: 1px solid #f1f5f9;
            border-bottom: 1px solid #f1f5f9;
        }

        .meta-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
            font-size: 12px;
            line-height: 1.4;
        }

        .meta-item:last-child {
            margin-bottom: 0;
        }

        .meta-label {
            font-weight: n00;
            color: #374151;
            flex-shrink: 0;
            text-align: center;
            transition: background 0.3s ease;
        }
.mea-value {
            color: #6b7280;
            font-weight: 500;
            text-align: right;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 底部信息优化 */
        .bottom-info {
            font-size: 10px;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            padding: 6px 0;
            background: #f9fafb;
            border-radius: 0px;
            padding: 6px 8px;
        }

        .info-item {
            font-weight: 500;
            letter-spacing: 0.3px;
        }

        /* 按钮优化 */
        .script-btn {
            width: 100%;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 6px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(52, 152, 219, 0.2);
        }

        .script-btn:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
        }

        /* 分页样式 */
        .pagination-wrapper {
            text-align: center;
             margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #eee;
        }

        .pagination-wrapper .pagination {
            display: inline-flex;
            gap: 8px;
            margin: 0;
        }

        .pagination-wrapper .pagination li {
            list-style: none;
        }

        .pagination-wrapper .pagination li a,
        .pagination-wrapper .pagination li span {
            display: block;
            padding: 8px 12px;
            color: #6b7280;
            text-decoration: none;
            border: 1px solid #e5e7eb;
            border-radius: 629;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .pagination-wrapper .pagination li a:hover {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination-wrapper .pagination li.active span {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        /* 样品数和销量样式优化 */
        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            padding: 8px 12px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            font-weight: 500;
            color: #495057;
        }

        .stat-item i {
            color: #3498db;
            font-size: 11px;
        }

        /* 佣金信息优化 */
        .commission-row {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
        }

        .commission-block {
            flex: 1;
            background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 6px 8px;
            text-align: center;
        }

        .commission-label {
            font-size: 10px;
            color: #6b7280;
            margin-bottom: 2px;
            font-weight: 500;
        }

        .commission-value {
            font-size: 13px;
            font-weight: 700;
            color: #dc2626;
        }

        /* 商家信息优化 */
        .meta-info {
            margin-bottom: 10px;
            padding: 8px 0;
            border-top: 1px solid #f1f5f9;
            border-bottom: 1px solid #f1f5f9;
        }

        .meta-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
            font-size: 12px;
            line-height: 1.4;
        }

        .meta-item:last-child {
            margin-bottom: 0;
        }

        .meta-label {
            font-weight: 600;
            color: #374151;
            flex-shrink: 0;
        }

        .meta-value {
            color: #6b7280;
            font-weight: 500;
            text-align: right;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 底部信息优化 */
        .bottom-info {
            font-size: 10px;
            color: #9ca3af;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            padding: 6px 0;
            background: #f9fafb;
            border-radius: 4px;
            padding: 6px 8px;
        }

        .info-item {
            font-weight: 500;
            letter-spacing: 0.3px;
        }

        /* 按钮优化 */
        .script-btn {
            width: 100%;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(52, 152, 219, 0.2);
        }

        .script-btn:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
        }

        .product-link-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-decoration: none;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-block;
            margin-top: 5px;
        }

        .product-link-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }

        .product-link-disabled {
            color: #999;
            font-size: 11px;
            font-style: italic;
        }

        /* 分页样式 */
        .pagination-wrapper {
            text-align: center;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid #eee;
        }

        .pagination-wrapper .pagination {
            display: inline-flex;
            gap: 8px;
            margin: 0;
        }

        .pagination-wrapper .pagination li {
            list-style: none;
        }

        .pagination-wrapper .pagination li a,
        .pagination-wrapper .pagination li span {
            display: block;
            padding: 8px 12px;
            color: #6b7280;
            text-decoration: none;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .pagination-wrapper .pagination li a:hover {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination-wrapper .pagination li.active span {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
    </style>
</head>
<body>
<div class="navbar">
    <a href="/index.php" class="logo">{$site.name|htmlentities}</a>

    <ul class="nav">
        <li><a href="/index.php" class="active">首页</a></li>
        {volist name="categories" id="cat"}
        <li>
            {if !empty($cat.children)}
                <a href="javascript:void(0)">{$cat.name|htmlentities}</a>
                <ul class="dropdown">
                    {volist name="cat.children" id="child"}
                    <li>
                        <a href="/index.php/index/index/category?id={$child.id}">{$child.name|htmlentities}</a>
       
             </li>
                    {/volist}
                </ul>
            {else/}
                <a href="/index.php/index/index/category?id={$cat.id}">{$cat.name|htmlentities}</a>
            {/if}
        </li>
        {/volist}
    </ul>
</div>

<div class="main-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
        <h1 class="welcome-title">欢迎来到TAP精品商城</h1>
        <p class="welcome-subtitle">发现生活中的美好，享受购物的乐趣</p>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
        <div class="search-container">
            <form action="/index.php/index/index/search" method="get" class="search-form">
                <input type="text" name="keyword" placeholder="搜索产品..." class="search-input" value="{$keyword|default=''}">
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>

    <!-- 产品列表 -->
    <div class="products-container">
        {if isset($products) && !empty($products)}
        <div class="products-header">
            <h2 class="products-title">热门产品</h2>
            <span class="products-count">共 {$products|count} 个产品</span>
        </div>
        
        <div class="product-list">
            {volist name="products" id="product"}
            <div class="product-card" data-script="{$product.video_text|default=''}">
                <div class="image-container">
                    <img src="{$product.image|htmlentities}" alt="{$product.name|htmlentities}" onerror="this.style.display='none'; this.parentNode.innerHTML='<div style=\'font-size:48px;color:#ccc;\'><i class=\'fas fa-image\'></i></div>';">
                </div>
                <div class="content">
                    <div class="title">{$product.name|htmlentities}</div>
                    <div class="price">Rp{$product.price}</div>
                    
                    <!-- 样品数和销量 -->
                    <div class="stats-row">
                        <div class="stat-item">
                            <i class="fas fa-box"></i>
                            <span>可用样品: {$product.sample_num|default=0}</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-chart-line"></i>
                            <span>销量: {$product.sale_count|default=0}</span>
                        </div>
                    </div>
                    
                    <div class="commission-row">
                        <div class="commission-block">
                            <div class="commission-label">公开佣金</div>
                            <div class="commission-value">{$product.public_commission}%</div>
                        </div>
                        <div class="commission-block">
                            <div class="commission-label">TAP佣金</div>
                            <div class="commission-value">{$product.commission}%</div>
                        </div>
                    </div>
                    <div class="meta-info">
                        <span class="meta-label">商家名称：</span>{$product.merchant.name|default='未知'}<br>
                        <span class="meta-label">跟进者：</span>{$product.admin.nickname|default='-'}
                    </div>
                    <div class="bottom-info">
                        <span>活动ID：{$product.active_id|default='-'}</span>
                        {if $product.product_id}
                        <a href="https://shop-id.tokopedia.com/view/product/{$product.product_id}" target="_blank" class="product-link-btn">查看产品链接</a>
                        {else/}
                        <span class="product-link-disabled">产品链接不可用</span>
                        {/if}
                    </div>
                    <button class="script-btn" onclick="showScript(this)">查看脚本</button>
                </div>
            </div>
            {/volist}
        </div>
        
        <!-- 分页 -->
        {if isset($pagination)}
        <div class="pagination-wrapper">
            {$pagination}
        </div>
        {/if}
        {else/}
        <div class="no-products">
            <i class="fas fa-box-open"></i>
            <div style="font-size:24px;font-weight:600;margin-bottom:10px;">暂无产品</div>
            <div>请稍后再来查看~</div>
        </div>
        {/if}
    </div>
</div>

<!-- 页脚 -->
<footer class="footer">
    <p>&copy; 2024 {$site.name|htmlentities}. 保留所有权利.</p>
    <p style="margin-top:5px;opacity:0.8;">让购物成为一种享受</p>
</footer>

<script>
// 查看脚本功能
function showScript(btn) {
    const card = btn.closest('.product-card');
    const script = card.getAttribute('data-script');
    
    if (script && script.trim()) {
        // 创建模态框显示脚本
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;
        
        modal.innerHTML = `
            <div style="
                background: white;
                border-radius: 8px;
                padding: 30px;
                max-width: 600px;
                max-height: 80vh;
                overflow-y: auto;
                position: relative;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            ">
                <button onclick="this.closest('.modal').remove()" style="
                    position: absolute;
                    top: 15px;
                    right: 15px;
                    background: none;
                    border: none;
                    font-size: 20px;
                    cursor: pointer;
                    color: #999;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                ">×</button>
                <h3 style="margin-bottom: 20px; color: #333;">产品脚本</h3>
                <div style="
                    background: #f8f9fa;
                    border-radius: 6px;
                    padding: 20px;
                    font-family: monospace;
                    font-size: 14px;
                    line-height: 1.6;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                    color: #333;
                    border: 1px solid #e9ecef;
                ">${script}</div>
            </div>
        `;
        
        modal.className = 'modal';
        document.body.appendChild(modal);
        
        // 点击背景关闭
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.remove();
            }
        });
    } else {
        alert('该产品暂无脚本信息');
    }
}
</script>
</body>
</html>

