<!-- jQ<PERSON>y (fastadmin一般已自动引入，这里仅作演示，如已引入可省略) -->
<!-- <script src="/assets/js/jquery.min.js"></script> -->
<!-- Bootstrap (如有) -->
<!-- <script src="/assets/js/bootstrap.min.js"></script> -->
<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <div class="form-group">
        <label for="c-pid" class="control-label col-xs-12 col-sm-2">上级菜单:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-pid" data-rule="required" class="form-control selectpicker" name="row[pid]">
                {foreach name="parentList" item="vo"}
                <option value="{$key|htmlentities}" {in name="key" value="$row.pid"}selected{/in}>{$vo.name|strip_tags|htmlentities}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="c-name" class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <label class="radio-inline">
                <input type="radio" name="row[status]" value="normal" {if $row.status=='normal'}checked{/if}> 正常
            </label>
            <label class="radio-inline">
                <input type="radio" name="row[status]" value="hidden" {if $row.status=='hidden'}checked{/if}> 隐藏
            </label>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
