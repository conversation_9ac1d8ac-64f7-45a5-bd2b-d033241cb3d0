define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {
    var Controller = {
        index: function () {
            Table.api.init({
                extend: {
                    index_url: 'category/index',
                    add_url: 'category/add',
                    edit_url: 'category/edit',
                    del_url: 'category/del',
                    multi_url: 'category/multi',
                }
            });
            var table = $("#table");
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                columns: [
                    {field: 'id', title: 'ID'},
                    {field: 'name', title: '分类名称'},
                    {
                        field: 'status',
                        title: '状态',
                        align: 'center',
                        formatter: function (value, row, index) {
                            return value == 1 ? '<span class="label label-success">开启</span>' : '<span class="label label-default">关闭</span>';
                        }
                    },
                    {field: 'operate', title: '操作', table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                ]
            });
            // 监听switch切换（如果有）
            table.on('change', '.category-switch', function () {
                var id = $(this).data('id');
                var status = $(this).prop('checked') ? 1 : 0;
                $.post('category/switchStatus', {id: id, status: status}, function (res) {
                    if (res.code !== 1) {
                        Toastr.error(res.msg || '操作失败');
                    } else {
                        Toastr.success('操作成功');
                    }
                });
            });
            Table.api.bindevent(table);
        }
    };
    return Controller;
}); 