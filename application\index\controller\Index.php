<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\Category;
use app\admin\model\Product;

class Index extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    /**
     * 首页
     */
    public function index()
    {
        try {
            // 获取分类数据
            $categories = Category::where('status', 'normal')
                ->order('weigh desc,id asc')
                ->select();
            $categories = collection($categories)->toArray();
            $categories = list_to_tree($categories, 'id', 'pid', 'children');

            // 调试：输出分类数据
            // var_dump('Categories count: ' . count($categories));
            // var_dump($categories);

            // 获取产品数据 - 添加分页
            $page = max(1, (int)$this->request->get('page', 1));
            $perPage = 12; // 每页显示12个产品
            
            $productsPage = Product::with(['catelog', 'merchant', 'admin'])
                ->where('product.status', 1)
                ->order('product.id desc')
                ->paginate($perPage, false, ['page' => $page]);
            
            $products = $productsPage->items();
            $pagination = $productsPage->render();
            
            $this->view->assign('categories', $categories);
            $this->view->assign('products', $products);
            $this->view->assign('pagination', $pagination);
            
            return $this->view->fetch();
        } catch (\Exception $e) {
            return '页面加载出错: ' . $e->getMessage();
        }
    }

    /**
     * 分类页面
     */
    public function category($id = null)
    {
        try {
            if (!$id) {
                $this->view->assign('msg', '分类不存在');
                $this->view->assign('products', []);
                return $this->view->fetch('category');
            }
            
            // 获取分类信息
            $category = Category::where('id', $id)->where('status', 'normal')->find();
            if (!$category) {
                $this->view->assign('msg', '分类不存在');
                $this->view->assign('products', []);
                return $this->view->fetch('category');
            }
            
            // 获取所有分类用于导航
            $categories = Category::where('status', 'normal')->where('pid', 0)->order('weigh desc,id desc')->select();
            foreach ($categories as &$cat) {
                $cat['children'] = Category::where('status', 'normal')->where('pid', $cat['id'])->order('weigh desc,id desc')->select();
            }
            
            // 获取该分类下的所有产品（包括子分类）
            $categoryIds = [$id];
            $subCategories = Category::where('pid', $id)->where('status', 'normal')->select();
            foreach ($subCategories as $subCat) {
                $categoryIds[] = $subCat['id'];
            }
            // 获取排序和筛选参数
            $sort = $this->request->get('sort', 'id');
            $commission = $this->request->get('commission', '');

            // 构建查询
            $query = Product::with(['catelog', 'merchant', 'admin'])
                ->where('product.status', 1)
                ->whereIn('product.catelog_id', $categoryIds);

            // 佣金筛选
            if ($commission) {
                switch ($commission) {
                    case '10':
                        $query->where('product.commission', '>=', 10);
                        break;
                    case '15':
                        $query->where('product.commission', '>=', 15);
                        break;
                    case '20':
                        $query->where('product.commission', '>=', 20);
                        break;
                }
            }

            // 排序
            switch ($sort) {
                case 'price_asc':
                    $query->order('product.price asc');
                    break;
                case 'price_desc':
                    $query->order('product.price desc');
                    break;
                case 'commission_desc':
                    $query->order('product.commission desc');
                    break;
                case 'sales_desc':
                    $query->order('product.sale_count desc');
                    break;
                default:
                    $query->order('product.id desc');
                    break;
            }

            // 分页参数
            $page = max(1, (int)$this->request->get('page', 1));
            $perPage = 12;
            $productsPage = $query->paginate($perPage, false, ['page' => $page]);
            $products = $productsPage->items();
            $pagination = $productsPage->render();
            $this->view->assign('categories', $categories);
            $this->view->assign('currentCategory', $category);
            $this->view->assign('products', $products);
            $this->view->assign('pagination', $pagination);
            $this->view->assign('currentSort', $sort);
            $this->view->assign('currentCommission', $commission);
            return $this->view->fetch('category');
        } catch (\Exception $e) {
            return '数据库查询错误: ' . $e->getMessage();
        }
    }

    /**
     * 搜索产品
     */
    public function search()
    {
        try {
            $keyword = trim($this->request->get('keyword', ''));
            
            if (empty($keyword)) {
                $this->redirect('/index.php');
            }
            
            // 获取分类数据
            $categories = Category::where('status', 'normal')
                ->order('weigh desc,id asc')
                ->select();
            $categories = collection($categories)->toArray();
            $categories = list_to_tree($categories, 'id', 'pid', 'children');
            
            // 搜索产品
            $page = max(1, (int)$this->request->get('page', 1));
            $perPage = 12;
            $productsPage = Product::with(['catelog', 'merchant', 'admin'])
                ->where('product.status', 1)
                ->where('product.name', 'like', '%' . $keyword . '%')
                ->order('product.id desc')
                ->paginate($perPage, false, ['page' => $page]);
            $products = $productsPage->items();
            $pagination = $productsPage->render();
            
            $this->view->assign('categories', $categories);
            $this->view->assign('products', $products);
            $this->view->assign('pagination', $pagination);
            $this->view->assign('keyword', $keyword);
            $this->view->assign('searchTitle', '搜索结果');
            
            return $this->view->fetch('search');
        } catch (\Exception $e) {
            return '搜索出错: ' . $e->getMessage();
        }
    }

}



