<?php
/**
 * FastAdmin性能优化脚本
 * 运行方式: php optimize.php
 */

echo "FastAdmin性能优化脚本\n";
echo "==================\n\n";

// 1. 清理缓存
echo "1. 清理缓存文件...\n";
$cacheDir = __DIR__ . '/runtime/cache';
if (is_dir($cacheDir)) {
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($cacheDir),
        RecursiveIteratorIterator::LEAVES_ONLY
    );
    
    $count = 0;
    foreach ($files as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            unlink($file->getRealPath());
            $count++;
        }
    }
    echo "   清理了 {$count} 个缓存文件\n";
}

// 2. 清理日志文件
echo "2. 清理日志文件...\n";
$logDir = __DIR__ . '/runtime/log';
if (is_dir($logDir)) {
    $files = glob($logDir . '/*.log');
    $count = 0;
    foreach ($files as $file) {
        if (filesize($file) > 10 * 1024 * 1024) { // 大于10MB的日志文件
            unlink($file);
            $count++;
        }
    }
    echo "   清理了 {$count} 个大日志文件\n";
}

// 3. 优化数据库
echo "3. 数据库优化建议...\n";
echo "   请手动执行以下SQL优化语句:\n";
echo "   OPTIMIZE TABLE fa_admin;\n";
echo "   OPTIMIZE TABLE fa_auth_rule;\n";
echo "   OPTIMIZE TABLE fa_config;\n";
echo "   ANALYZE TABLE fa_admin;\n";

// 4. 检查PHP配置
echo "4. 检查PHP配置...\n";
$recommendations = [];

if (ini_get('opcache.enable') != '1') {
    $recommendations[] = "建议启用OPcache: opcache.enable=1";
}

if (ini_get('memory_limit') < '256M') {
    $recommendations[] = "建议增加内存限制: memory_limit=256M";
}

if (ini_get('max_execution_time') < 60) {
    $recommendations[] = "建议增加执行时间: max_execution_time=60";
}

if (empty($recommendations)) {
    echo "   PHP配置良好\n";
} else {
    echo "   PHP配置建议:\n";
    foreach ($recommendations as $rec) {
        echo "   - {$rec}\n";
    }
}

// 5. 检查扩展
echo "5. 检查PHP扩展...\n";
$requiredExtensions = ['redis', 'opcache', 'gd', 'curl', 'mbstring'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

if (empty($missingExtensions)) {
    echo "   所有必需扩展已安装\n";
} else {
    echo "   缺少扩展: " . implode(', ', $missingExtensions) . "\n";
}

echo "\n优化完成！\n";
echo "建议重启Web服务器以使配置生效。\n";
