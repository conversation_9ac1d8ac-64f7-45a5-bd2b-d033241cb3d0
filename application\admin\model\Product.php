<?php

namespace app\admin\model;

use think\Model;


class Product extends Model
{

    

    

    // 表名
    protected $name = 'product';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'datetime';

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







    public function admin()
    {
        return $this->belongsTo('Admin', 'admin_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function merchant()
    {
        return $this->belongsTo('Merchant', 'merchant_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function catelog()
    {
        return $this->belongsTo('app\\common\\model\\Category', 'catelog_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    protected static function init()
    {
        parent::init();
        self::beforeInsert(function ($row) {
            $now = date('Y-m-d H:i:s');
            if (empty($row['create_time'])) {
                $row['create_time'] = $now;
            }
            if (empty($row['update_time'])) {
                $row['update_time'] = $now;
            }
            // 自动写入当前登录管理员id
            if (empty($row['admin_id'])) {
                $adminId = session('admin.id');
                if ($adminId) {
                    $row['admin_id'] = $adminId;
                }
            }
        });
        self::beforeUpdate(function ($row) {
            $row['update_time'] = date('Y-m-d H:i:s');
        });
    }
}
