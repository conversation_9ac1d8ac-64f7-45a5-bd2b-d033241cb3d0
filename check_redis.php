<?php
/**
 * Redis连接检查脚本
 */

echo "Redis连接检查\n";
echo "=============\n\n";

// 检查Redis扩展
if (!extension_loaded('redis')) {
    echo "❌ Redis扩展未安装\n";
    echo "请安装Redis扩展: \n";
    echo "Ubuntu/Debian: sudo apt-get install php-redis\n";
    echo "CentOS/RHEL: sudo yum install php-redis\n";
    echo "Windows: 下载php_redis.dll并添加到php.ini\n";
    exit(1);
}

echo "✅ Redis扩展已安装\n";

// 尝试连接Redis
try {
    $redis = new Redis();
    $redis->connect('127.0.0.1', 6379);
    
    // 测试连接
    $redis->ping();
    echo "✅ Redis连接成功\n";
    
    // 测试写入
    $redis->set('test_key', 'test_value', 10);
    $value = $redis->get('test_key');
    
    if ($value === 'test_value') {
        echo "✅ Redis读写测试成功\n";
        $redis->del('test_key');
    } else {
        echo "❌ Redis读写测试失败\n";
    }
    
    // 显示Redis信息
    $info = $redis->info();
    echo "\nRedis信息:\n";
    echo "版本: " . $info['redis_version'] . "\n";
    echo "内存使用: " . round($info['used_memory'] / 1024 / 1024, 2) . " MB\n";
    echo "连接数: " . $info['connected_clients'] . "\n";
    
    $redis->close();
    
} catch (Exception $e) {
    echo "❌ Redis连接失败: " . $e->getMessage() . "\n";
    echo "\n解决方案:\n";
    echo "1. 确保Redis服务已启动\n";
    echo "2. 检查Redis配置文件\n";
    echo "3. 确认端口6379未被占用\n";
    echo "4. 检查防火墙设置\n";
    exit(1);
}

echo "\n✅ Redis配置检查完成\n";
