<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Description')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-description" data-rule="required" class="form-control editor" rows="5" name="row[description]" cols="50">{$row.description|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-image" data-rule="required" class="form-control" size="50" name="row[image]" type="text" value="{$row.image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-image" class="btn btn-danger faupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-price" data-rule="required" class="form-control" step="0.01" name="row[price]" type="number" value="{$row.price|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Public_commission')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-public_commission" data-rule="required" class="form-control" step="0.01" name="row[public_commission]" type="number" value="{$row.public_commission|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Commission')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-commission" data-rule="required" class="form-control" step="0.01" name="row[commission]" type="number" value="{$row.commission|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sample_total')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sample_total" data-rule="required" class="form-control" name="row[sample_total]" type="number" value="{$row.sample_total|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sample_num')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sample_num" data-rule="required" class="form-control" name="row[sample_num]" type="number" value="{$row.sample_num|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">视频脚本:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-video_text" class="form-control editor" name="row[video_text]" rows="4">{$row.video_text|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <label class="radio-inline">
                <input type="radio" name="row[status]" value="1" {if $row.status==1}checked{/if}> 已上架
            </label>
            <label class="radio-inline">
                <input type="radio" name="row[status]" value="0" {if $row.status==0}checked{/if}> 未上架
            </label>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Product_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-product_id" data-rule="required" class="form-control" name="row[product_id]" type="text" value="{$row.product_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Active_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-active_id" data-rule="required" class="form-control" name="row[active_id]" type="text" value="{$row.active_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Catelog_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-catelog_id" data-rule="required" data-source="category/selectpage" data-params='{"isleaf":1}' class="form-control selectpage" name="row[catelog_id]" type="text" value="{$row.catelog_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Merchant_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-merchant_id" data-rule="required" data-source="merchant/index" class="form-control selectpage" name="row[merchant_id]" type="text" value="{$row.merchant_id|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
