define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'category/index',
                    add_url: 'category/add',
                    edit_url: 'category/edit',
                    del_url: 'category/del',
                    table: 'category',
                }
            });

            var table = $("#table");
            var tableOptions = {
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                pagination: false,
                commonSearch: false,
                search: false,
                idField: 'id',
                parentIdField: 'pid',
                treeShowField: 'name',
                treeEnable: true,
                columns: [
                    [
                        {field: 'id', title: __('Id'), width: 60},
                        {field: 'name', title: __('分类名称'), align: 'left', formatter: function(value, row, index) {
                            // 去除所有 &nbsp; 并解码 HTML 实体
                            var txt = value.replace(/&nbsp;/g, '');
                            var div = document.createElement('div');
                            div.innerHTML = txt;
                            return div.textContent || div.innerText || '';
                        }},
                        {field: 'status', title: __('Status'), formatter: Table.api.formatter.status},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: function (value, row, index) {
                            var html = '';
                            // 添加子分类按钮
                            html += '<a href="'+$.fn.bootstrapTable.defaults.extend.add_url+'?pid='+row.id+'" class="btn btn-xs btn-dialog btn-success btn-addsub" title="添加子分类"><i class="fa fa-plus"></i></a> ';
                            // 编辑按钮
                            html += '<a href="'+$.fn.bootstrapTable.defaults.extend.edit_url+'/ids/'+row.id+'" class="btn btn-xs btn-info btn-editone" title="编辑"><i class="fa fa-pencil"></i></a> ';
                            // 删除按钮
                            html += '<a href="'+$.fn.bootstrapTable.defaults.extend.del_url+'/ids/'+row.id+'" class="btn btn-xs btn-danger btn-delone" title="删除"><i class="fa fa-trash"></i></a>';
                            return html;
                        }}
                    ]
                ]
            };
            // 初始化表格
            table.bootstrapTable(tableOptions);

            // 为表格绑定事件
            Table.api.bindevent(table);

            //绑定TAB事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var typeStr = $(this).attr("href").replace('#', '');
                var options = table.bootstrapTable('getOptions');
                options.pageNumber = 1;
                options.queryParams = function (params) {
                    params.type = typeStr;
                    return params;
                };
                table.bootstrapTable('refresh', {});
                return false;
            });

            //必须默认触发shown.bs.tab事件
            // $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");

        },
        add: function () {
            Controller.api.bindevent();
            setTimeout(function () {
                $("#c-type").trigger("change");
            }, 100);
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                $(document).on("change", "#c-type", function () {
                    $("#c-pid option[data-type='all']").prop("selected", true);
                    $("#c-pid option").removeClass("hide");
                    $("#c-pid option[data-type!='" + $(this).val() + "'][data-type!='all']").addClass("hide");
                    $("#c-pid").data("selectpicker") && $("#c-pid").selectpicker("refresh");
                });
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
