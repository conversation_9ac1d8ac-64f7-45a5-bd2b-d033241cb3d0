<?php

namespace app\admin\middleware;

use think\Request;
use think\Response;

/**
 * 性能监控中间件
 */
class Performance
{
    public function handle(Request $request, \Closure $next)
    {
        // 记录开始时间和内存使用
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        // 设置内存限制
        ini_set('memory_limit', '256M');
        
        // 启用输出缓冲
        if (!ob_get_level()) {
            ob_start();
        }
        
        // 执行请求
        $response = $next($request);
        
        // 计算执行时间和内存使用
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        
        $executionTime = round(($endTime - $startTime) * 1000, 2); // 毫秒
        $memoryUsage = round(($endMemory - $startMemory) / 1024 / 1024, 2); // MB
        
        // 在开发模式下添加性能信息到响应头
        if (config('app_debug')) {
            $response->header([
                'X-Execution-Time' => $executionTime . 'ms',
                'X-Memory-Usage' => $memoryUsage . 'MB',
                'X-Peak-Memory' => round(memory_get_peak_usage() / 1024 / 1024, 2) . 'MB'
            ]);
        }
        
        // 记录慢请求
        if ($executionTime > 1000) { // 超过1秒的请求
            \think\Log::record("Slow request: {$request->url()} - {$executionTime}ms", 'notice');
        }
        
        return $response;
    }
}
